import Entry from "./entry";
import h from "@/app/helpers/all";

/**
 * Represents an entity within the domain with optional token and dynamic properties
 */
interface Entity {
  /** Unique token identifier for the entity */
  token?: string;
  /** Allow any additional properties on the entity */
  [key: string]: any;
}

/**
 * Domain class manages a collection of entities with token-based identification
 * and provides functionality for entity manipulation, field access, and current entity tracking.
 *
 * This class serves as a central registry for entities, allowing for:
 * - Dynamic entity creation and management
 * - Token-based entity identification
 * - Current entity context tracking
 * - Field path resolution with bracket notation support
 * - Entry expansion through the Entry class integration
 *
 * @example
 * ```typescript
 * const domain = new Domain({ user: { name: "<PERSON>", age: 30 } });
 * domain.setEntity("[user][email]", "<EMAIL>");
 * const userEmail = domain.getEntityFieldValue("[user][email]");
 * ```
 */
export default class Domain {
  /** Map of entity IDs to their corresponding entity objects */
  public readonly entities: Map<string, Entity>;

  /** Currently active entity ID for context-aware operations */
  private currentEntityId: string | null;

  /** Additional data storage for domain-level information */
  public data: Record<string, any> = {};

  /**
   * Creates a new Domain instance with initial entity data
   * @param initialData - Object containing initial entities to populate the domain
   */
  constructor(initialData: Record<string, any> = {}) {
    this.entities = new Map();
    this.currentEntityId = null;

    // Initialize entities from provided data, ensuring each has a token
    Object.entries(initialData).forEach(([key, value]) => {
      // Handle both object and primitive values
      const entityValue = h.isObject(value) ? value : { value };
      this.entities.set(key, {
        ...entityValue,
        token: (entityValue as any).token || this.generateToken(),
      });
    });
  }

  /**
   * Generates a unique token for entity identification
   * @returns A random 4-digit string token
   */
  private generateToken(): string {
    return h.randomNumber();
  }

  /**
   * Checks if an entity exists in the domain
   * @param key - The entity key to check
   * @returns True if the entity exists, false otherwise
   */
  isEntity(key: string): boolean {
    return this.entities.has(key);
  }

  /**
   * Sets a field value for an entity using dot notation or bracket notation path
   * Supports nested field paths like "entityId.field.subfield" or "[entityId][field][subfield]"
   * Creates the entity if it doesn't exist and sets it as current
   *
   * @param fieldPath - Dot notation or bracket notation path to the field
   * @param fieldValue - Value to set, will be processed through Entry expansion
   */
  setEntity(fieldPath: string, fieldValue: any): void {
    // Parse both bracket notation and dot notation into keys array
    let keys: string[];

    if (fieldPath.includes("[") && fieldPath.includes("]")) {
      // Handle bracket notation: "[entityId][field][subfield]"
      keys = fieldPath
        .replace(/^\[/g, "")
        .replace(/\]$/g, "")
        .replace(/\] *\[/g, ".")
        .split(".");
    } else {
      // Handle dot notation: "entityId.field.subfield"
      keys = fieldPath.split(".");
    }

    const entityId = keys[0];

    // Expand the field value through Entry processing
    const entry = new Entry(fieldValue);
    const value = entry.expandedEntry;

    // Get or create the target entity
    const entity = this.getOrCreateEntity(entityId);

    // Update the entity with the nested field path
    if (keys.length === 1) {
      // Setting the entire entity
      if (h.isObject(value)) {
        Object.assign(entity, value);
      } else {
        // If setting a primitive value to the entity root, store it directly
        this.entities.set(entityId, { ...entity, ...{ value } });
      }
    } else {
      // Setting a nested field
      this.updateEntityWithKeys(entity, keys.slice(1), value);
    }

    // Set this entity as the current context
    this.setCurrentEntity(entityId);
  }

  /**
   * Retrieves an existing entity or creates a new one with a generated token
   * @param entityId - The ID of the entity to get or create
   * @returns The entity object
   */
  private getOrCreateEntity(entityId: string): Entity {
    if (!this.entities.has(entityId)) {
      this.entities.set(entityId, { token: this.generateToken() });
    }
    return this.entities.get(entityId)!;
  }

  /**
   * Updates an entity with nested field values using a key path
   * Handles token replacement in string values
   *
   * @param entity - The entity to update
   * @param keys - Array of keys representing the nested path
   * @param value - The value to set at the final key
   */
  private updateEntityWithKeys(
    entity: Entity,
    keys: string[],
    value: any
  ): void {
    let current = entity;

    // Navigate to the parent of the target field, creating objects as needed
    for (let i = 0; i < keys.length - 1; i++) {
      current[keys[i]] = current[keys[i]] || {};
      current = current[keys[i]];
    }

    const lastKey = keys[keys.length - 1];

    // Set the final value, handling token replacement for strings
    if (h.isObject(value)) {
      current[lastKey] = value;
    } else {
      // Replace "token" placeholder with actual entity token
      current[lastKey] =
        typeof value === "string"
          ? value.replace("token", entity.token || "")
          : value;
    }
  }

  /**
   * Retrieves a field value from an entity using bracket notation reference
   * Supports nested field access and Entry expansion at each level
   *
   * @param reference - Bracket notation reference (e.g., "[user][profile][name]")
   * @returns The resolved field value after Entry expansion
   */
  getEntityFieldValue(reference: string): any {
    // Parse bracket notation into dot notation keys
    const keys = reference
      .replace(/^\[/g, "")
      .replace(/\]$/g, "")
      .replace(/\] *\[/g, ".")
      .split(".");

    // Navigate through the key path, expanding entries at each level
    const value = keys.reduce((result: any, part: string) => {
      let fieldValue = h.getObjectFieldValueWithNormalizedKey(result, part);
      const entry = new Entry(fieldValue);
      fieldValue = entry.expandedEntry;
      return fieldValue;
    }, Object.fromEntries(this.entities));

    return value;
  }

  /**
   * Gets the ID of the currently active entity
   * @returns The current entity ID or null if none is set
   */
  getCurrentEntityId(): string | null {
    return this.currentEntityId;
  }

  /**
   * Clears the current entity context
   */
  resetCurrentEntityId(): void {
    this.currentEntityId = null;
  }

  /**
   * Retrieves the currently active entity object
   * @returns The current entity or null if none is set or entity doesn't exist
   */
  getCurrentEntity(): Entity | null {
    if (!this.currentEntityId) return null;
    return this.entities.get(this.currentEntityId) || null;
  }

  /**
   * Gets a field value from the currently active entity
   * Uses normalized key matching for flexible field access
   *
   * @param entityField - The field name to retrieve (supports dot notation)
   * @returns The field value or empty string if entity/field doesn't exist
   */
  getCurrentEntityFieldValue(entityField: string): any {
    const currentEntity = this.getCurrentEntity();
    if (!currentEntity) return "";
    return h.getObjectFieldValueWithNormalizedKey(currentEntity, entityField);
  }

  /**
   * Sets the current entity context, creating the entity if it doesn't exist
   * @param entityId - The ID of the entity to set as current
   */
  setCurrentEntity(entityId: string): void {
    this.getOrCreateEntity(entityId);
    this.currentEntityId = entityId;
  }

  /**
   * Ensures the current entity has a token, generating one if needed
   * Only affects entities that don't already have a token
   */
  setCurrentEntityToken(): void {
    const currentEntity = this.getCurrentEntity();
    if (currentEntity && !currentEntity.token) {
      currentEntity.token = this.generateToken();
    }
  }

  /**
   * Gets the token of the currently active entity
   * @returns The current entity's token or null if no current entity or no token
   */
  getCurrentEntityToken(): string | null {
    const currentEntity = this.getCurrentEntity();
    return currentEntity?.token || null;
  }

  /**
   * Checks if the current entity contains a specific field
   * Uses normalized key matching for flexible field checking
   *
   * @param entityField - The field name to check for (supports dot notation)
   * @returns True if the field exists and has a truthy value, false otherwise
   */
  checkCurrentEntityContainsField(entityField: string): boolean {
    const currentEntity = this.getCurrentEntity();
    if (!currentEntity) return false;
    return !!h.getObjectFieldValueWithNormalizedKey(currentEntity, entityField);
  }

  /**
   * Returns all entities as a plain object
   * Useful for serialization or debugging
   *
   * @returns Object containing all entities with their IDs as keys
   */
  getAllEntities(): Record<string, Entity> {
    return Object.fromEntries(this.entities);
  }
}
