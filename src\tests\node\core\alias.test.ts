import { Alias } from "../../../app/core/alias";
import Tester from "../tester";

const testSets = {
  parse: {
    getResult: function () {
      const sdt = { aliases: this.aliases, usedAliases: [] };
      return new Alias(sdt, this.label).parsedLabel;
    },
    tests: {
      test01: {
        aliases: [["alias1", "content1"]],
        label: "«alias1»",
        expectedResult: "content1",
      },
      test02: {
        aliases: [
          ["alias1", "content1"],
          ["alias2", "content2"],
        ],
        label: "«alias1» «alias2»",
        expectedResult: "content1 content2",
      },
      test10: {
        aliases: [
          ["alias1", "«alias2»"],
          ["alias2", "content2"],
        ],
        label: "«alias1»",
        expectedResult: "content2",
      },
      test11: {
        aliases: [
          ["alias1", "«alias2»"],
          ["alias2", "«alias3»"],
          ["alias3", "content3"],
        ],
        label: "«alias1»",
        expectedResult: "content3",
      },
      test20: {
        aliases: [["alias1", "content1 $p"]],
        label: "«alias1 (arg1)»",
        expectedResult: "content1 arg1",
      },
      test21: {
        aliases: [["alias1", "content1 $p $q"]],
        label: "«alias1 (arg1, arg2)»",
        expectedResult: "content1 arg1 arg2",
      },
      test22: {
        aliases: [
          ["alias1", "«alias2 ($p1, $q1)»"],
          ["alias2", "content2 $p2 $q2"],
        ],
        label: "«alias1 (arg1, arg2)»",
        expectedResult: "content2 arg1 arg2",
      },
    },
  },
};

const tester = new Tester(testSets);
tester.run();
