import Parser from "@/app/core/parser";
import Tester from "@/tests/node/tester";
import h from "@/app/helpers/all";

const testSets = {
  setEntity: {
    getResult: function () {
      const parser = new Parser();
      const result = parser.parse(this.text);
      if (h.isObject(result)) delete result["token"];
      return result;
    },
    tests: {
      test01: {
        text: null,
        tables: {},
        expectedResult: null,
      },
      test02: {
        text: undefined,
        tables: {},
        expectedResult: undefined,
      },
      test03: {
        text: "",
        tables: {},
        expectedResult: "",
      },
      test04: {
        text: 1,
        tables: {},
        expectedResult: 1,
      },
      test05: {
        text: "a",
        tables: {},
        expectedResult: "a",
      },
      test06: {
        text: true,
        tables: {},
        expectedResult: true,
      },
      test07: {
        text: "a\\[b\\]c\\,d\\:e",
        tables: {},
        expectedResult: "a[b]c\\,d\\:e",
      },
      test10: {
        text: "[User1][First Name]",
        tables: {
          User1: { Id: "User1", "First Name": "<PERSON>", "Last Name": "Smith" },
        },
        expectedResult: "John",
      },
      test11: {
        text: "[User1][First Name] [User1][Last Name]",
        tables: {
          User1: { Id: "User1", "First Name": "John", "Last Name": "Smith" },
        },
        expectedResult: "John Smith",
      },
      test12: {
        text: " [User1][First Name] [User1][Last Name] ",
        tables: {
          User1: { Id: "User1", "First Name": "John", "Last Name": "Smith" },
        },
        expectedResult: " John Smith ",
      },
      test13: {
        text: "First name is [User1][First Name] and last name is [User1][Last Name]",
        tables: {
          User1: { Id: "User1", "First Name": "John", "Last Name": "Smith" },
        },
        expectedResult: "First name is John and last name is Smith",
      },
      test20: {
        text: "[First Name]",
        tables: {
          User1: { Id: "User1", "First Name": "John", "Last Name": "Smith" },
          User2: { Id: "User2", "First Name": "Mary", "Last Name": "Doe" },
          Office1: { Id: "Office1", Manager: "[User2]" },
          currentEntityId: "User1",
        },
        expectedResult: "John",
      },
      test21: {
        text: "Office1 manager is [Office1][Manager][First Name]",
        tables: {
          User1: { Id: "User1", "First Name": "John", "Last Name": "Smith" },
          Office1: {
            Id: "Office1",
            Manager: { "First Name": "Mary", "Last Name": "Doe" },
          },
          currentEntityId: "User1",
        },
        expectedResult: "Office1 manager is Mary",
      },
      test22: {
        text: "Office1 manager is [Office1][Manager][First Name]",
        tables: {
          User1: { Id: "User1", "First Name": "John", "Last Name": "Smith" },
          User2: { Id: "User2", "First Name": "Mary", "Last Name": "Doe" },
          Office1: { Id: "Office1", Manager: "[User2]" },
          currentEntityId: "User1",
        },
        expectedResult: "Office1 manager is Mary",
      },
      test40: {
        text: "[User1]",
        tables: {
          User1: { Id: "User1", "First Name": "John", "Last Name": "Smith" },
        },
        expectedResult: {
          Id: "User1",
          "First Name": "John",
          "Last Name": "Smith",
        },
      },
    },
  },
};

const tester = new Tester(testSets);
tester.run();
