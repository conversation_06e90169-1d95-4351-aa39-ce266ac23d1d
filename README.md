# SDT (Script Driven Tests)

[![Cypress](https://img.shields.io/badge/cypress-14.5.1-green.svg)](https://cypress.io)
[![TypeScript](https://img.shields.io/badge/typescript-5.5.2-blue.svg)](https://www.typescriptlang.org/)
[![Node.js](https://img.shields.io/badge/node.js-required-green.svg)](https://nodejs.org/)

SDT is a powerful, extensible test automation framework built on top of Cypress that enables **Script Driven Testing** through Excel-based test definitions. It allows non-programmers to define, manage, and execute automated tests using familiar spreadsheet formats while leveraging the robust capabilities of Cypress for web application testing.

## Table of Contents

- [Features](#features)
- [Project Overview](#project-overview)
- [Installation](#installation)
- [Quick Start](#quick-start)
- [Usage](#usage)
- [Test Structure](#test-structure)
- [Configuration](#configuration)
- [Extensions](#extensions)
- [Documentation](#documentation)
- [Contributing](#contributing)
- [Troubleshooting](#troubleshooting)
- [License](#license)

## Features

- 📊 **Excel-based Test Definition**: Define tests, scripts, and data tables in Excel files
- 🔧 **Extensible Architecture**: Support for multiple project extensions (Riverstar, JBA, RWA, etc.)
- 🎯 **Cypress Integration**: Built on Cypress for reliable web automation
- 📸 **Screenshot Support**: Automatic screenshot capture on errors and titles
- 🗄️ **Database Integration**: MongoDB support for data-driven testing
- 🔄 **Script Reusability**: Define reusable scripts and call them from tests
- 📋 **Comprehensive Reporting**: Detailed test execution reports with results
- 🎨 **GUI and Headless Modes**: Run tests interactively or in CI/CD pipelines

## Project Overview

SDT solves the challenge of maintaining large-scale test suites by allowing non-technical team members to define tests in Excel while providing developers with a powerful, code-based execution engine. The framework supports:

- **Multi-project architecture** with dedicated extensions for different applications
- **Data-driven testing** through Excel tables and MongoDB integration
- **Modular test design** with reusable scripts and components
- **Flexible execution** with GUI and headless modes

## Installation

### Prerequisites

- Node.js (version 14 or higher)
- npm or yarn package manager
- Git (for cloning repositories)

### Setup

1. **Clone the repository:**

   ```bash
   git clone https://github.com/riverstar/sdt.git
   cd sdt
   ```

2. **Install dependencies:**

   ```bash
   npm install
   ```

3. **Verify installation:**

   ```bash
   npm run sdt:gui
   ```

## Quick Start

1. **Create a test configuration** in your project folder:

   ```json
   {
     "company": "your-company",
     "app": "your-app-name",
     "appUrl": "http://localhost:4200",
     "sdtFile": "your-tests.xlsx"
   }
   ```

2. **Create an Excel test file** with the following structure:

   - **Tests sheet**: Define your test cases with columns for Run, Test, Use Case, and Steps
   - **Scripts sheet**: Define reusable scripts
   - **Tables sheet**: Define test data tables
   - **Elements sheet**: Define UI element selectors
3. **Run tests:**

   ```bash
   # Interactive mode
   npm run sdt:gui

   # Headless mode
   npm run sdt:headless
   ```

## Usage

### Running Tests

**GUI Mode (Interactive):**

```bash
npm run sdt:gui
```

Opens Cypress Test Runner for interactive test development and debugging.

**Headless Mode (CI/CD):**

```bash
npm run sdt:headless
```

Runs tests in headless mode suitable for continuous integration.

### Project-Specific Execution

Each project has its own run script that handles application setup and test execution:

```bash
# Navigate to project folder
cd projects/riverstar/riverstar

# Run project-specific tests
./run.cmd
```

## Test Structure

### Excel File Format

SDT uses Excel files to define tests with the following sheets:

#### Tests Sheet

| Run | Test | Use Case | Action | Target       | Values | Do |
| --- | ---- | -------- | ------ | ------------ | ------ | -- |
| x   | 1    | Login    | Click  | Login Button |        | x  |

#### Scripts Sheet

| Script | Action | Target   | Values | Do |
| ------ | ------ | -------- | ------ | -- |
| Login  | Type   | Username | admin  | x  |

#### Tables Sheet

| Id | Field1 | Field2 |
| -- | ------ | ------ |
| 1  | Value1 | Value2 |

#### Elements Sheet

| Item        | Selector   | Root | Locator |
| ----------- | ---------- | ---- | ------- |
| LoginButton | #login-btn |      |         |

### Test Execution Flow

1. **Initialization**: Load extension and configuration
2. **Test Discovery**: Parse Excel file and extract tests
3. **Test Execution**: Run tests using Cypress
4. **Reporting**: Generate execution reports and screenshots

## Configuration

### Environment Variables

- `RUN_FOLDER`: Project execution directory
- `CONFIG_PATH`: Path to configuration JSON file
- `APP`: Application name for extension loading
- `RSD_DB_URL`: MongoDB connection string (optional)

### Configuration File Options

```json
{
  "company": "company-name",
  "app": "application-name", 
  "appRepo": "https://github.com/user/app.git",
  "appUrl": "http://localhost:4200",
  "appScript": "npm start",
  "packageManager": "npm",
  "sdtFile": "tests.xlsx",
  "takeScreenshotOnError": "yes",
  "takeScreenshotOnTitle": "no"
}
```

## Extensions

SDT supports multiple project extensions:

- **Riverstar**: CE, Billing, PWV, SRM applications
- **Dorian Solutions**: JBA (Job Board Aggregator)
- **Other**: RWA (Real World App) for demos

Each extension provides:

- Custom actions and utility functions
- Application-specific setup and teardown
- API handlers for backend integration
- UI element definitions and icons

## Documentation

- [Test Definition Guide](docs/test-definition.md) - How to create tests in Excel
- [Extension Development](docs/extensions.md) - Creating custom extensions
- [API Reference](docs/api-reference.md) - Available actions and utility functions
- [Configuration Guide](docs/configuration.md) - Detailed configuration options

## Contributing

We welcome contributions! Please follow these guidelines:

### Reporting Issues

1. Check existing issues before creating new ones
2. Provide detailed reproduction steps
3. Include environment information and error logs
4. Use issue templates when available

### Submitting Pull Requests

1. Fork the repository and create a feature branch
2. Follow existing code style and conventions
3. Add tests for new functionality
4. Update documentation as needed
5. Submit PR with clear description of changes

### Development Setup

```bash
# Clone and setup
git clone https://github.com/riverstar/sdt.git
cd sdt
npm install

# Run tests
npm test

# Lint code
npm run lint
```

## Troubleshooting

### Common Issues

**Tests not loading:**

- Verify Excel file path in configuration
- Check file format and required sheets exist
- Ensure proper column headers

**Application not starting:**

- Verify application URL and script configuration
- Check if required ports are available
- Review application logs for startup errors

**Extension not found:**

- Confirm extension name matches configuration
- Verify extension files exist in projects folder
- Check import paths in extension files

### FAQ

**Q: Can I run tests without an Excel file?**
A: No, SDT requires Excel files for test definitions. This is a core feature of the framework.

**Q: How do I add custom actions?**
A: Create or extend an extension with custom actions in the actions object.

**Q: Can I use different databases?**
A: Currently, SDT supports MongoDB. Other databases would require custom implementation.

## License

This project is licensed under the GNU General Public License (GPL) version 3 or any later version.

You are free to use, modify, and distribute this software under the terms of the GPL.

By contributing to this project, you agree that your contributions will be licensed under the GNU General Public License (GPL) version 3 or later. You affirm that you have the right to grant this license and that your contributions do not infringe on the rights of others. This allows the project to freely use, modify, and distribute your contributions under the terms of the GPL.

See the [GNU General Public License](https://www.gnu.org/licenses/gpl-3.0.html) for more details.

## Contact

- **Issues**: [GitHub Issues](https://github.com/riverstar/sdt/issues)
- **Discussions**: [GitHub Discussions](https://github.com/riverstar/sdt/discussions)
- **Email**: <<EMAIL>>

---

## Built with ❤️ by the Riverstar Team
