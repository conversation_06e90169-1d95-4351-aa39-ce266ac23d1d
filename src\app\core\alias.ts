import h from "@/app/helpers/all";

/**
 * Parses and resolves label aliases within a text string.
 *
 * This function processes alias references enclosed in French guillemets (« »)
 * and replaces them with their corresponding values from the aliases object.
 * It supports:
 * - Parameter substitution using $variable syntax
 * - Nested alias resolution (recursive processing)
 * - Argument parsing with comma separation
 * - Special character handling († as comma placeholder)
 *
 * @param label - The text containing alias references to process
 * @param aliases - Object containing alias definitions (key-value pairs)
 * @param usedAliases - Array to track which aliases have been used (mutated)
 * @returns The processed text with aliases resolved
 *
 * @example
 * // Given aliases: { "greeting": "Hello $name", "user": "<PERSON>" }
 * parseLabelAliases("«greeting(«user»)»", aliases, [])
 * // Returns: "Hello John"
 */
export default function parseLabelAliases(
  label: string,
  aliases: Record<string, string>,
  usedAliases: string[]
): string {
  if (!label || typeof label !== "string") {
    return label || "";
  }

  if (!aliases || typeof aliases !== "object") {
    return label;
  }

  // Process all alias references in the label using regex replacement
  return label.replace(
    /«([^»]+?)»/g,
    (fullMatch: string, aliasContent: string) => {
      // Extract argument block if present: «aliasName(arg1, arg2)»
      const argsBlock = aliasContent.match(/\(.*[\s\S]*?\)/)?.[0];

      // Parse arguments if they exist
      const args =
        argsBlock
          ?.replace(/^\((.*)\)$/, "$1") // Remove outer parentheses
          .split(",")
          .map((arg) => h.parseQuotedStringsAndEscapedCharacters(arg.trim())) ||
        [];

      // Extract the alias name by removing guillemets and arguments
      const aliasLabel = aliasContent
        .replace(argsBlock || "", "") // Remove argument block
        .trim();

      // Look up the alias definition using normalized key matching
      let foundAlias = h.getObjectFieldValueWithNormalizedKey(
        aliases,
        aliasLabel
      );

      // Track usage of this alias (avoid duplicates)
      if (usedAliases && !usedAliases.includes(aliasLabel)) {
        usedAliases.push(aliasLabel);
        usedAliases.sort(); // Keep the list sorted for consistency
      }

      // If alias not found, return the original match
      if (foundAlias === undefined || foundAlias === null) {
        return fullMatch;
      }

      // Convert to string for processing
      foundAlias = String(foundAlias);

      // Replace parameter placeholders with provided arguments
      let paramIndex = 0;
      foundAlias = foundAlias.replace(/\$[\w\d]+/g, () => {
        const replacement = args[paramIndex] ?? "";
        paramIndex++;
        // Convert † back to comma (special character handling)
        return replacement.replace(/†/g, ",");
      });

      // Remove any remaining ## markers and clean up whitespace
      foundAlias = foundAlias.replace(/##/g, "").trim();

      // Recursively process nested aliases within the resolved content
      foundAlias = foundAlias.replace(/«(.*?)»/g, (nestedMatch: string) =>
        parseLabelAliases(nestedMatch, aliases, usedAliases)
      );

      return foundAlias;
    }
  );
}

/**
 * Legacy Alias class for backward compatibility with existing tests.
 *
 * This class wraps the parseLabelAliases function to maintain compatibility
 * with the existing test suite while providing the improved functionality.
 *
 * @deprecated Use parseLabelAliases function directly for new code
 */
export class Alias {
  public readonly parsedLabel: string;

  /**
   * Creates a new Alias instance for backward compatibility
   * @param sdt - SDT context object containing aliases and usedAliases
   * @param label - The label string to parse
   */
  constructor(
    sdt: { aliases: string[][]; usedAliases: string[] },
    label: string
  ) {
    // Convert array-based aliases to object format
    const aliasesObject: Record<string, string> = {};
    if (sdt.aliases && Array.isArray(sdt.aliases)) {
      sdt.aliases.forEach(([key, value]) => {
        if (key && value) {
          aliasesObject[key] = value;
        }
      });
    }

    // Parse the label using the improved function
    this.parsedLabel = parseLabelAliases(
      label,
      aliasesObject,
      sdt.usedAliases || []
    );
  }
}
