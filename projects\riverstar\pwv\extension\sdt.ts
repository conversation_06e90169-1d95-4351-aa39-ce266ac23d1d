import estensionTaskHandler from "./handlers/taskHandler";
import extensionActions from "./core/actions";
import extensionApiHandler from "./handlers/apiHandler";
import extensionFunctions from "./core/functions";
import extensionIcons from "./core/icons";
import extensionScheduleHandler from "./handlers/scheduleHandler";
import extensionSetup from "./core/setup";
import extensionStepperHandler from "./handlers/stepperHandler";
import h from "@/app/helpers/all";
import riverstarSdt from "../../.organization/sdt";

export default {
  setup: extensionSetup,
  config: riverstarSdt.config,
  icons: h.mergeObjects(riverstarSdt.icons, extensionIcons),
  actions: h.mergeObjects(riverstarSdt.actions, extensionActions),
  functions: h.mergeObjects(riverstarSdt.functions, extensionFunctions),
  apiHandler: h.mergeObjects(riverstarSdt.apiHandler, extensionApiHandler),
  taskHandler: estension<PERSON><PERSON>Handler,
  stepperHandler: extension<PERSON>tep<PERSON><PERSON><PERSON><PERSON>,
  scheduleHandler: extensionScheduleHandler,
} as const;
