/**
 * Core locator strategies for finding elements in Cypress tests.
 * Each locator method follows a consistent pattern:
 * 1. Find elements using a base selector
 * 2. Filter elements based on specific criteria (content, attributes, etc.)
 * 3. Apply index selection if specified
 * 4. Validate element existence and run validation if provided
 * 5. Return wrapped Cypress element
 */

import type Element from "@/app/core/element";

/**
 * Type definition for validation functions passed to locators
 */
type ValidationFunction = (element: JQuery<HTMLElement>) => void;

/**
 * Type definition for locator methods
 */
type LocatorMethod = (
  parent: Cypress.Chainable<JQuery<HTMLElement>>,
  element: Element,
  validation?: ValidationFunction
) => Cypress.Chainable<JQuery<HTMLElement>>;

/**
 * Interface for the locators object
 */
interface ILocators {
  byContent: LocatorMethod;
  byLabel: LocatorMethod;
  byPrecedingLabel: LocatorMethod;
  byPlaceholder: LocatorMethod;
  byCheckboxText: LocatorMethod;
  byCheckboxEmbeddedText: LocatorMethod;
  byTooltip: LocatorMethod;
}

/**
 * Helper function to normalize text content for comparison
 * @param text - Text to normalize
 * @returns Normalized text with newlines removed
 */
const normalizeText = (text: string): string => text.replace(/\n/g, "");

/**
 * Helper function to check if content matches based on partial/exact matching
 * @param actualText - The actual text content from the element
 * @param expectedContent - The expected content to match
 * @param isPartial - Whether to use partial matching
 * @param shouldTrim - Whether to trim whitespace before comparison
 * @returns True if content matches
 */
const isContentMatch = (
  actualText: string,
  expectedContent: string,
  isPartial: boolean,
  shouldTrim: boolean = false
): boolean => {
  const actual = shouldTrim ? actualText.trim() : actualText;
  const expected = shouldTrim ? expectedContent.trim() : expectedContent;

  if (isPartial) {
    return actual.includes(expected);
  }

  return normalizeText(actual) === normalizeText(expected);
};

/**
 * Helper function to apply element selection and validation
 * @param $elem - jQuery element collection
 * @param element - Element configuration object
 * @param validation - Optional validation function
 * @returns Selected element after applying index and validation
 */
const applySelectionAndValidation = (
  $elem: JQuery<HTMLElement>,
  element: Element,
  validation?: ValidationFunction
): JQuery<HTMLElement> => {
  let $foundElem = $elem;

  // Apply index selection if specified (1-based index)
  if (element.index) {
    const indexNum = parseInt(element.index, 10);
    if (isNaN(indexNum) || indexNum < 1) {
      throw new Error(
        `Invalid element index: ${element.index}. Index must be a positive number.`
      );
    }
    $foundElem = $elem.eq(indexNum - 1);
  }

  // Validate element existence
  expect($foundElem.length).to.be.at.least(1);

  // Run validation if this is the last element and validation is provided
  if (element.isLast && validation) {
    validation($foundElem);
  }

  return $foundElem;
};

const locators: ILocators = {
  /**
   * Locates elements by their text content
   * Supports both exact and partial text matching based on target configuration
   *
   * @param parent - Parent Cypress chainable to search within
   * @param element - Element configuration containing selector and content
   * @param validation - Optional validation function to run on found element
   * @returns Cypress chainable for the located element
   */
  byContent(parent, element, validation?) {
    if (!element.selector) {
      throw new Error("Element selector is required for byContent locator");
    }

    let $foundElem: JQuery<HTMLElement>;

    return parent
      .find(element.selector)
      .filter((_index, item) => {
        // If no content specified, match all elements
        if (!element.content) {
          return true;
        }

        const isPartial = Cypress.sdt.current.step.target.isPartial;
        return isContentMatch(item.innerText, element.content, isPartial);
      })
      .should(($elem) => {
        $foundElem = applySelectionAndValidation($elem, element, validation);
      })
      .then(() => cy.wrap($foundElem));
  },

  /**
   * Locates elements by finding their associated label and then finding the target element
   * within the same root container. Useful for form fields with labels.
   *
   * @param parent - Parent Cypress chainable to search within
   * @param element - Element configuration containing selector, content, and root
   * @param validation - Optional validation function to run on found element
   * @returns Cypress chainable for the located element
   */
  byLabel(parent, element, validation?) {
    if (!element.selector || !element.root) {
      throw new Error(
        "Element selector and root are required for byLabel locator"
      );
    }

    let $foundElem: JQuery<HTMLElement>;
    const labelSelector = element.isVisible ? "label:visible" : "label";

    return parent
      .find(labelSelector)
      .filter((_index, item) => {
        // If no content specified, match all labels
        if (!element.content) {
          return true;
        }

        const isPartial = Cypress.sdt.current.step.target.isPartial;
        return isContentMatch(item.innerText, element.content, isPartial);
      })
      .should(($elem) => {
        // Apply index selection to label elements
        let $selectedLabel = $elem;
        if (element.index) {
          const indexNum = parseInt(element.index, 10);
          if (isNaN(indexNum) || indexNum < 1) {
            throw new Error(
              `Invalid element index: ${element.index}. Index must be a positive number.`
            );
          }
          $selectedLabel = $elem.eq(indexNum - 1);
        }

        // Find the target element within the root container
        const cleanSelector = element.selector.replace(":visible", "");
        $foundElem = $selectedLabel.closest(element.root).find(cleanSelector);

        expect($foundElem.length).to.be.at.least(1);
        if (element.isLast && validation) {
          validation($foundElem);
        }
      })
      .then(() => cy.wrap($foundElem));
  },

  /**
   * Locates elements by finding their preceding label and then selecting the next sibling element.
   * Useful for form layouts where labels precede their associated inputs.
   *
   * @param parent - Parent Cypress chainable to search within
   * @param element - Element configuration containing content to match in label
   * @param validation - Optional validation function to run on found element
   * @returns Cypress chainable for the located element
   */
  byPrecedingLabel(parent, element, validation?) {
    let $foundElem: JQuery<HTMLElement>;
    const labelSelector = element.isVisible ? "label:visible" : "label";

    return parent
      .find(labelSelector)
      .filter((_index, item) => {
        // If no content specified, match all labels
        if (!element.content) {
          return true;
        }

        const isPartial = Cypress.sdt.current.step.target.isPartial;
        return isContentMatch(item.innerText, element.content, isPartial);
      })
      .should(($elem) => {
        // Apply index selection to label elements
        let $selectedLabel = $elem;
        if (element.index) {
          const indexNum = parseInt(element.index, 10);
          if (isNaN(indexNum) || indexNum < 1) {
            throw new Error(
              `Invalid element index: ${element.index}. Index must be a positive number.`
            );
          }
          $selectedLabel = $elem.eq(indexNum - 1);
        }

        // Get the next sibling element
        $foundElem = $selectedLabel.next();

        expect($foundElem.length).to.be.at.least(1);
        if (element.isLast && validation) {
          validation($foundElem);
        }
      })
      .then(() => cy.wrap($foundElem));
  },

  /**
   * Locates elements by their placeholder or data-placeholder attributes
   * Supports both exact and partial matching based on target configuration
   *
   * @param parent - Parent Cypress chainable to search within
   * @param element - Element configuration containing selector and content
   * @param validation - Optional validation function to run on found element
   * @returns Cypress chainable for the located element
   */
  byPlaceholder(parent, element, validation?) {
    if (!element.selector) {
      throw new Error("Element selector is required for byPlaceholder locator");
    }

    let $foundElem: JQuery<HTMLElement>;
    const isPartial = Cypress.sdt.current.step.target.isPartial;

    return parent
      .find(element.selector)
      .filter((_index, item) => {
        const $item = Cypress.$(item);
        const placeholder = $item.attr("placeholder") || "";
        const dataPlaceholder = $item.attr("data-placeholder") || "";

        if (!element.content) {
          return true;
        }

        if (isPartial) {
          return (
            placeholder.includes(element.content) ||
            dataPlaceholder.includes(element.content)
          );
        }

        return (
          placeholder === element.content || dataPlaceholder === element.content
        );
      })
      .should(($elem) => {
        $foundElem = applySelectionAndValidation($elem, element, validation);
      })
      .then(() => cy.wrap($foundElem));
  },

  /**
   * Locates checkboxes by finding div elements with matching text content,
   * then finding the checkbox in the parent container
   *
   * @param parent - Parent Cypress chainable to search within
   * @param element - Element configuration containing content to match
   * @param validation - Optional validation function to run on found element
   * @returns Cypress chainable for the located checkbox element
   */
  byCheckboxText(parent, element, validation?) {
    let $foundElem: JQuery<HTMLElement>;
    const isVisible = Cypress.sdt.current.step.target.isVisible;
    const divSelector = isVisible ? "div:visible" : "div";

    return parent
      .find(divSelector)
      .filter((_index, item) => {
        // If no content specified, match all divs
        if (!element.content) {
          return true;
        }

        const isPartial = Cypress.sdt.current.step.target.isPartial;
        return isContentMatch(item.innerText, element.content, isPartial, true);
      })
      .last() // Take the last matching div
      .parent()
      .find("input[type='checkbox']")
      .should(($elem) => {
        $foundElem = applySelectionAndValidation($elem, element, validation);
      })
      .then(() => cy.wrap($foundElem));
  },

  /**
   * Locates checkboxes by finding div elements with matching text content,
   * then finding the checkbox embedded within the same div
   *
   * @param parent - Parent Cypress chainable to search within
   * @param element - Element configuration containing content to match
   * @param validation - Optional validation function to run on found element
   * @returns Cypress chainable for the located checkbox element
   */
  byCheckboxEmbeddedText(parent, element, validation?) {
    let $foundElem: JQuery<HTMLElement>;
    const isVisible = Cypress.sdt.current.step.target.isVisible;
    const divSelector = isVisible ? "div:visible" : "div";

    return parent
      .find(divSelector)
      .filter((_index, item) => {
        // If no content specified, match all divs
        if (!element.content) {
          return true;
        }

        const isPartial = Cypress.sdt.current.step.target.isPartial;
        return isContentMatch(item.innerText, element.content, isPartial, true);
      })
      .last() // Take the last matching div
      .find("input[type='checkbox']")
      .should(($elem) => {
        $foundElem = applySelectionAndValidation($elem, element, validation);
      })
      .then(() => cy.wrap($foundElem));
  },

  /**
   * Locates elements by their Angular Material tooltip (mattooltip) attribute
   * Supports both exact and partial matching based on target configuration
   *
   * @param parent - Parent Cypress chainable to search within
   * @param element - Element configuration containing selector and content
   * @param validation - Optional validation function to run on found element
   * @returns Cypress chainable for the located element
   */
  byTooltip(parent, element, validation?) {
    if (!element.selector) {
      throw new Error("Element selector is required for byTooltip locator");
    }

    let $foundElem: JQuery<HTMLElement>;
    const isPartial = Cypress.sdt.current.step.target.isPartial;

    return parent
      .find(element.selector)
      .filter((_index, item) => {
        const tooltip = Cypress.$(item).attr("mattooltip") || "";

        if (!element.content) {
          return true;
        }

        if (isPartial) {
          return tooltip.includes(element.content);
        }

        return tooltip === element.content;
      })
      .should(($elem) => {
        $foundElem = applySelectionAndValidation($elem, element, validation);
      })
      .then(() => cy.wrap($foundElem));
  },
};

export default locators;
